package com.github.continuedev.continueintellijextension.services

import com.github.continuedev.continueintellijextension.constants.getConfigJsPath
import com.github.continuedev.continueintellijextension.constants.getConfigJsonPath
import com.intellij.execution.multilaunch.design.components.RoundedCornerBorder
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.ServiceManager
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.DumbAware
import com.intellij.util.concurrency.AppExecutorUtil
import com.intellij.util.messages.Topic
import com.intellij.util.ui.JBUI
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import java.awt.FlowLayout
import java.awt.Font
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import java.io.File
import java.io.IOException
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit
import javax.swing.*

class ContinueSettingsComponent : DumbAware {
    private val logger = LoggerManager.getLogger(ContinueSettingsComponent::class.java)

    val panel: JPanel = JPanel(GridBagLayout())
    val remoteConfigServerUrl: JTextField = JTextField()
    val remoteConfigSyncPeriod: JTextField = JTextField()
    val userToken: JTextField = JTextField()
    val enableTabAutocomplete: JCheckBox = JCheckBox("开启自动补全功能")
    val enableOSR: JCheckBox = JCheckBox("离屏渲染")
    val displayEditorTooltip: JCheckBox = JCheckBox("展示编辑小工具")
    val showIDECompletionSideBySide: JCheckBox = JCheckBox("Show IDE completions side-by-side")

    // 新增的UI组件
    val completionTypeComboBox: JComboBox<String> = JComboBox(arrayOf("多行补全", "单行补全"))
    val shortcutLabel: JPanel = createShortcutKeyPanel()
    val indexProgressBar: JProgressBar = JProgressBar(0, 100)
    val deleteIndexButton: JButton = JButton("删除索引")
    val refreshIndexButton: JButton = JButton("刷新索引")

    private fun createShortcutKeyPanel(): JPanel {
        val panel = JPanel(FlowLayout(FlowLayout.LEFT, 5, 0))
        panel.isOpaque = false

        val textLabel = JLabel("触发补全快捷键: ")

        // 请求补全
        val keyLabel = label("请求补全 ⌘\\")

        // 采纳补全
        val acceptLabel = label("采纳补全 ⇥")
        
        panel.add(textLabel)
        panel.add(keyLabel)
        panel.add(acceptLabel)
        
        return panel
    }

    init {
        val constraints = GridBagConstraints()
        constraints.fill = GridBagConstraints.HORIZONTAL
        constraints.weightx = 1.0
        constraints.weighty = 0.0
        constraints.gridx = 0
        constraints.gridy = 0
        constraints.insets = JBUI.insets(5)

        // 现有的配置项
        panel.add(enableTabAutocomplete, constraints)
        constraints.gridy++
        panel.add(enableOSR, constraints)
        constraints.gridy++

        // 补全类型选择
        val completionTypeLabel = JLabel("补全类型:")
        panel.add(completionTypeLabel, constraints)
        constraints.gridy++
        panel.add(completionTypeComboBox, constraints)
        constraints.gridy++

        // 快捷键展示
        panel.add(shortcutLabel, constraints)
        constraints.gridy++

        // 仓库实时索引部分
        val indexLabel = JLabel("仓库实时索引:")
        panel.add(indexLabel, constraints)
        constraints.gridy++

        // 进度条
        indexProgressBar.isStringPainted = true
        indexProgressBar.string = "索引进度: 0%"
        panel.add(indexProgressBar, constraints)
        constraints.gridy++

        // 索引操作按钮面板
        val buttonPanel = JPanel()
        buttonPanel.add(deleteIndexButton)
        buttonPanel.add(refreshIndexButton)
        panel.add(buttonPanel, constraints)
        constraints.gridy++

        // 按钮事件监听
        deleteIndexButton.addActionListener { e ->
            val result = JOptionPane.showConfirmDialog(
                panel,
                "确定要删除索引吗？这将清除所有已建立的代码索引。",
                "确认删除",
                JOptionPane.YES_NO_OPTION
            )
            if (result == JOptionPane.YES_OPTION) {
                // 触发删除索引操作
                deleteIndex()
            }
        }

        refreshIndexButton.addActionListener { e ->
            // 触发刷新索引操作
            refreshIndex()
        }

        // Add a "filler" component that takes up all remaining vertical space
        constraints.weighty = 1.0
        val filler = JPanel()
        panel.add(filler, constraints)
    }

    private fun deleteIndex() {
        // 模拟删除索引操作
        SwingUtilities.invokeLater {
            indexProgressBar.value = 0
            indexProgressBar.string = "索引已删除"
            JOptionPane.showMessageDialog(panel, "索引删除成功！", "提示", JOptionPane.INFORMATION_MESSAGE)
        }
    }

    private fun refreshIndex() {
        // 模拟刷新索引操作
        SwingUtilities.invokeLater {
            indexProgressBar.value = 0
            indexProgressBar.string = "正在刷新索引..."

            // 模拟进度更新
            val timer = Timer(100) { e ->
                val currentValue = indexProgressBar.value
                if (currentValue < 100) {
                    indexProgressBar.value = currentValue + 2
                    indexProgressBar.string = "索引进度: ${indexProgressBar.value}%"
                } else {
                    (e.source as Timer).stop()
                    indexProgressBar.string = "索引完成"
                    JOptionPane.showMessageDialog(panel, "索引刷新完成！", "提示", JOptionPane.INFORMATION_MESSAGE)
                }
            }
            timer.start()
        }
    }

    fun updateIndexProgress(progress: Int) {
        SwingUtilities.invokeLater {
            indexProgressBar.value = progress
            indexProgressBar.string = "索引进度: $progress%"
        }
    }
}

@Serializable
class ContinueRemoteConfigSyncResponse {
    var configJson: String? = null
    var configJs: String? = null
}

@State(
    name = "com.github.continuedev.continueintellijextension.services.ContinueExtensionSettings",
    storages = [Storage("ContinueExtensionSettings.xml")]
)
open class ContinueExtensionSettings : PersistentStateComponent<ContinueExtensionSettings.ContinueState> {
    private val logger = LoggerManager.getLogger(ContinueExtensionSettings::class.java)

    class ContinueState {
        var lastSelectedInlineEditModel: String? = null
        var shownWelcomeDialog: Boolean = false
        var remoteConfigServerUrl: String? = null
        var remoteConfigSyncPeriod: Int = 60
        var userToken: String? = null
        var enableTabAutocomplete: Boolean = true
        var enableOSR: Boolean = shouldRenderOffScreen()
        var displayEditorTooltip: Boolean = true
        var showIDECompletionSideBySide: Boolean = false
        var continueTestEnvironment: String = "production"

        // 新增的配置字段
        var completionType: String = "多行补全"
        var indexProgress: Int = 0
    }

    var continueState: ContinueState = ContinueState()

    private var remoteSyncFuture: ScheduledFuture<*>? = null

    override fun getState(): ContinueState {
        return continueState
    }

    override fun loadState(state: ContinueState) {
        continueState = state
    }

    companion object {
        val instance: ContinueExtensionSettings
            get() = ServiceManager.getService(ContinueExtensionSettings::class.java)
    }


    // Sync remote config from server
    private fun syncRemoteConfig() {
        val state = instance.continueState

        if (state.remoteConfigServerUrl != null && state.remoteConfigServerUrl!!.isNotEmpty()) {
            // download remote config as json file

            val client = OkHttpClient()
            val baseUrl = state.remoteConfigServerUrl?.removeSuffix("/")

            val requestBuilder = Request.Builder().url("${baseUrl}/sync")

            if (state.userToken != null) {
                requestBuilder.addHeader("Authorization", "Bearer ${state.userToken}")
            }

            val request = requestBuilder.build()
            var configResponse: ContinueRemoteConfigSyncResponse? = null

            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) throw IOException("Unexpected code $response")

                    response.body?.string()?.let { responseBody ->
                        try {
                            configResponse =
                                Json.decodeFromString<ContinueRemoteConfigSyncResponse>(responseBody)
                        } catch (e: Exception) {
                            logger.error("Exception occurred", e)
                            return
                        }
                    }
                }
            } catch (e: IOException) {
                logger.error("Exception occurred", e)
                return
            }

            if (configResponse?.configJson?.isNotEmpty()!!) {
                val file = File(getConfigJsonPath(request.url.host))
                file.writeText(configResponse!!.configJson!!)
            }

            if (configResponse?.configJs?.isNotEmpty()!!) {
                val file = File(getConfigJsPath(request.url.host))
                file.writeText(configResponse!!.configJs!!)
            }
        }
    }

    // Create a scheduled task to sync remote config every `remoteConfigSyncPeriod` minutes
    fun addRemoteSyncJob() {

        if (remoteSyncFuture != null) {
            remoteSyncFuture?.cancel(false)
        }

        instance.remoteSyncFuture = AppExecutorUtil.getAppScheduledExecutorService()
            .scheduleWithFixedDelay(
                { syncRemoteConfig() },
                0,
                continueState.remoteConfigSyncPeriod.toLong(),
                TimeUnit.MINUTES
            )
    }
}

interface SettingsListener {
    fun settingsUpdated(settings: ContinueExtensionSettings.ContinueState)

    companion object {
        val TOPIC = Topic.create("SettingsUpdate", SettingsListener::class.java)
    }
}

class ContinueExtensionConfigurable : Configurable {
    private var mySettingsComponent: ContinueSettingsComponent? = null

    override fun createComponent(): JComponent {
        mySettingsComponent = ContinueSettingsComponent()
        return mySettingsComponent!!.panel
    }

    override fun isModified(): Boolean {
        val settings = ContinueExtensionSettings.instance
        val modified =
            mySettingsComponent?.remoteConfigServerUrl?.text != settings.continueState.remoteConfigServerUrl ||
                    mySettingsComponent?.remoteConfigSyncPeriod?.text?.toInt() != settings.continueState.remoteConfigSyncPeriod ||
                    mySettingsComponent?.userToken?.text != settings.continueState.userToken ||
                    mySettingsComponent?.enableTabAutocomplete?.isSelected != settings.continueState.enableTabAutocomplete ||
                    mySettingsComponent?.enableOSR?.isSelected != settings.continueState.enableOSR ||
                    mySettingsComponent?.displayEditorTooltip?.isSelected != settings.continueState.displayEditorTooltip ||
                    mySettingsComponent?.showIDECompletionSideBySide?.isSelected != settings.continueState.showIDECompletionSideBySide ||
                    mySettingsComponent?.completionTypeComboBox?.selectedItem != settings.continueState.completionType
        return modified
    }

    override fun apply() {
        val settings = ContinueExtensionSettings.instance
        settings.continueState.remoteConfigServerUrl = mySettingsComponent?.remoteConfigServerUrl?.text
        settings.continueState.remoteConfigSyncPeriod = mySettingsComponent?.remoteConfigSyncPeriod?.text?.toInt() ?: 60
        settings.continueState.userToken = mySettingsComponent?.userToken?.text
        settings.continueState.enableTabAutocomplete = mySettingsComponent?.enableTabAutocomplete?.isSelected ?: false
        settings.continueState.enableOSR = mySettingsComponent?.enableOSR?.isSelected ?: true
        settings.continueState.displayEditorTooltip = mySettingsComponent?.displayEditorTooltip?.isSelected ?: true
        settings.continueState.showIDECompletionSideBySide =
            mySettingsComponent?.showIDECompletionSideBySide?.isSelected ?: false
        settings.continueState.completionType = mySettingsComponent?.completionTypeComboBox?.selectedItem as String? ?: "多行补全"

        ApplicationManager.getApplication().messageBus.syncPublisher(SettingsListener.TOPIC)
            .settingsUpdated(settings.continueState)
        ContinueExtensionSettings.instance.addRemoteSyncJob()
    }

    override fun reset() {
        val settings = ContinueExtensionSettings.instance
        mySettingsComponent?.remoteConfigServerUrl?.text = settings.continueState.remoteConfigServerUrl
        mySettingsComponent?.remoteConfigSyncPeriod?.text = settings.continueState.remoteConfigSyncPeriod.toString()
        mySettingsComponent?.userToken?.text = settings.continueState.userToken
        mySettingsComponent?.enableTabAutocomplete?.isSelected = settings.continueState.enableTabAutocomplete
        mySettingsComponent?.enableOSR?.isSelected = settings.continueState.enableOSR
        mySettingsComponent?.displayEditorTooltip?.isSelected = settings.continueState.displayEditorTooltip
        mySettingsComponent?.showIDECompletionSideBySide?.isSelected =
            settings.continueState.showIDECompletionSideBySide
        mySettingsComponent?.completionTypeComboBox?.selectedItem = settings.continueState.completionType

        ContinueExtensionSettings.instance.addRemoteSyncJob()
    }

    override fun disposeUIResources() {
        mySettingsComponent = null
    }

    override fun getDisplayName(): String {
        return "AIMI Settings"
    }
}

/**
 * This function checks if off-screen rendering (OSR) should be used.
 *
 * If ui.useOSR is set in config.json, that value is used.
 *
 * Otherwise, we check if the pluginSinceBuild is greater than or equal to 233, which corresponds
 * to IntelliJ platform version 2023.3 and later.
 *
 * Setting `setOffScreenRendering` to `false` causes a number of issues such as a white screen flash when loading
 * the GUI and the inability to set `cursor: pointer`. However, setting `setOffScreenRendering` to `true` on
 * platform versions prior to 2023.3.4 causes larger issues such as an inability to type input for certain languages,
 * e.g. Korean.
 *
 * References:
 * 1. https://youtrack.jetbrains.com/issue/IDEA-347828/JCEF-white-flash-when-tool-window-show#focus=Comments-27-9334070.0-0
 *    This issue mentions that white screen flash problems were resolved in platformVersion 2023.3.4.
 * 2. https://www.jetbrains.com/idea/download/other.html
 *    This documentation shows mappings from platformVersion to branchNumber.
 *
 * We use the branchNumber (e.g., 233) instead of the full version number (e.g., 2023.3.4) because
 * it's a simple integer without dot notation, making it easier to compare.
 */
private fun shouldRenderOffScreen(): Boolean {
    val minBuildNumber = 233
    val applicationInfo = ApplicationInfo.getInstance()
    val currentBuildNumber = applicationInfo.build.baselineVersion
    return currentBuildNumber >= minBuildNumber
}

private fun label(title: String): JLabel {
    val keyLabel = JLabel(title)
    keyLabel.isOpaque = false
    keyLabel.background = JBUI.CurrentTheme.ActionButton.hoverBackground()
    keyLabel.foreground = JBUI.CurrentTheme.Label.foreground()
    keyLabel.font = Font(Font.MONOSPACED, Font.PLAIN, keyLabel.font.size)
    keyLabel.border = RoundedCornerBorder(8)
    return keyLabel
}