package com.github.continuedev.continueintellijextension.services

import com.github.continuedev.continueintellijextension.`continue`.CoreMessenger
import com.github.continuedev.continueintellijextension.`continue`.CoreMessengerManager
import com.github.continuedev.continueintellijextension.`continue`.DiffManager
import com.github.continuedev.continueintellijextension.`continue`.IdeProtocolClient
import com.github.continuedev.continueintellijextension.toolWindow.ContinuePluginToolWindowFactory
import com.github.continuedev.continueintellijextension.utils.uuid
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.EDT
import com.intellij.openapi.components.Service
import com.intellij.openapi.observable.properties.AtomicProperty
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.wm.ToolWindow
import com.intellij.ui.jcef.JBCefBrowser
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import javax.swing.JComponent
import kotlin.properties.Delegates

@Service(Service.Level.PROJECT)
class ContinuePluginService : Disposable, DumbAware {
    private val logger = LoggerManager.getLogger(javaClass)
    private val pluginWindows = mutableMapOf<String, ContinuePluginToolWindowFactory.ContinuePluginWindow>()
    private var activePluginWindow: ContinuePluginToolWindowFactory.ContinuePluginWindow? = null
    val coroutineScope = CoroutineScope(Dispatchers.Default)
    var listeners = mutableListOf<(() -> Unit)?>()
    var ideProtocolClient: IdeProtocolClient? by Delegates.observable(null) { _, _, _ ->
        synchronized(this) {
            listeners.forEach {
                it?.invoke()
            }
            listeners.clear()
        }
    }
    lateinit var toolWindow: ToolWindow
    var coreMessengerManager = AtomicProperty<CoreMessengerManager?>(null)
    val coreMessenger: CoreMessenger?
        get() = coreMessengerManager.get()?.coreMessenger?.get()
    var workspacePaths: Array<String>? = null
    var windowId: String = uuid()
    var diffManager: DiffManager? = null
    val activeContent: JComponent?
        get() = activePluginWindow?.content

    val activeBrowser: JBCefBrowser?
        get() = activePluginWindow?.browser?.browser

    override fun dispose() {
        coroutineScope.cancel()
        coreMessenger?.coroutineScope?.let {
            it.cancel()
            coreMessenger?.killSubProcess()
        }
    }

    fun sendToWebview(
        messageType: String,
        data: Any?,
        messageId: String = uuid()
    ) {
        activePluginWindow?.browser?.sendToWebview(messageType, data, messageId)
    }

    /**
     * Add a listener for protocolClient initialization.
     * Currently, only one needs to be processed. If there are more than one,
     * we can use an array to add listeners to ensure that the message is processed.
     */
    fun onProtocolClientInitialized(listener: () -> Unit) {
        if (ideProtocolClient == null) {
            synchronized(this) {
                if (ideProtocolClient == null) {
                    this.listeners.add(listener)
                } else {
                    listener()
                }
            }
        } else {
            listener()
        }
    }

    fun addPluginWindow(window: ContinuePluginToolWindowFactory.ContinuePluginWindow, name: String) {
        pluginWindows[name] = window
        if (pluginWindows.size == 1) {
            activePluginWindow = window
        }
    }

    fun onPluginWindowChanged(name: String) {
        activePluginWindow = pluginWindows[name]
        activePluginWindow?.content?.requestFocus()
    }

    fun changeTitle(title: String) {
        coroutineScope.launch(Dispatchers.EDT) {
            activePluginWindow?.changeTitle(title)
        }
    }

    fun changeWindow(index: Int, url: String?, reload: Boolean = false) {
        coroutineScope.launch(Dispatchers.EDT) {
            val window = pluginWindows.values.toList().getOrNull(index) ?: return@launch
            val browser = window.browser.browser
            toolWindow.contentManager.setSelectedContent(window.contentWrapper)
            if (!url.isNullOrEmpty()) {
                browser.loadURL(url)
            } else if (reload) {
                logger.info("Reload requested for window: ${window.browser.url}")
                // 使用ContinueBrowser的reload方法，它会处理初始化状态检查
                window.browser.reload()
            }
        }
    }
}