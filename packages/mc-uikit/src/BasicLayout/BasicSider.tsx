import React, { useCallback, useMemo, useState } from 'react';
import { Flex, Grid, Layout, SiderProps } from 'antd';
import { ChevronLeftOutlined, ChevronRightOutlined } from '@ali/mc-icons';

const { useBreakpoint } = Grid;

import styles from './index.module.less';

export type BasicSiderProps = {
  /**
   * 侧边栏的位置，默认 left，主要用来做折叠按钮和其他一些操作区域的定位判断
   */
  position?: 'left' | 'right';

  /**
   * 侧边栏是否可折叠
   */
  collapsible?: boolean;

  /**
   * 侧边栏折叠状态改变时触发
   */
  onCollapse?: (collapsed: boolean) => void;
} & SiderProps;
export default function BasicSider(props: BasicSiderProps) {
  const { position = 'left', collapsible = false, onCollapse: onCollapseProp, children, ...rest } = props;

  const [collapsed, setCollapsed] = useState(false);

  const { xl } = useBreakpoint();

  const isSmallWindow = useMemo(() => {
    return !xl;
  }, [xl]);

  const onCollapse = useCallback(
    (_collapsed: boolean) => {
      setCollapsed(_collapsed);
      onCollapseProp?.(_collapsed);
    },
    [setCollapsed, onCollapseProp],
  );

  return (
    <Layout.Sider
      {...rest}
      trigger={null}
      collapsible={collapsible}
      collapsed={collapsed}
      className={styles.basicSider}
      collapsedWidth={20}
    >
      <Flex
        className={styles.collapseBtn}
        onClick={() => onCollapse(!collapsed)}
      >
        {collapsible &&
          (collapsed ? (
            position === 'left' ? (
              <ChevronRightOutlined />
            ) : (
              <ChevronLeftOutlined />
            )
          ) : position === 'left' ? (
            <ChevronLeftOutlined />
          ) : (
            <ChevronRightOutlined />
          ))}
      </Flex>
      <Flex
        className={styles.basicSiderWrapper}
        vertical
        style={{
          overflowX: 'hidden',
          width: rest.width,
          height: '100%',
          marginLeft: collapsed ? '-360px' : 0,
          padding: `var(--mc-padding-lg) ${isSmallWindow ? 'var(--mc-padding-lg)' : 'var(--mc-padding-lg)'}`,
          transition: 'margin-left 0.1s',
        }}
      >
        {children}
      </Flex>
    </Layout.Sider>
  );
}
