import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function NorthStarOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-north-star-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05122">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05122)">
          <path
            d="M8.5,0.75C8.5,0.335786,8.16421,0,7.75,0C7.33579,0,7,0.335786,7,0.75L7,5.93933L4.39125,3.33058C4.09836,3.03768,3.62348,3.03768,3.33059,3.33058C3.03769,3.62347,3.0377,4.09835,3.33059,4.39124L5.93935,7L0.75,7C0.335787,7,0,7.33579,0,7.75C0,8.16421,0.335787,8.5,0.75,8.5L5.93936,8.5L3.33059,11.1088C3.0377,11.4017,3.0377,11.8765,3.33059,12.1694C3.62349,12.4623,4.09836,12.4623,4.39125,12.1694L7,9.56068L7,14.75C7,15.1642,7.33579,15.5,7.75,15.5C8.16421,15.5,8.5,15.1642,8.5,14.75L8.5,9.56065L11.1088,12.1694C11.4017,12.4623,11.8765,12.4623,12.1694,12.1694C12.4623,11.8765,12.4623,11.4016,12.1694,11.1088L9.56067,8.5L14.75,8.5C15.1642,8.5,15.5,8.16421,15.5,7.75C15.5,7.33579,15.1642,7,14.75,7L9.56068,7L12.1694,4.39125C12.4623,4.09836,12.4623,3.62349,12.1694,3.33059C11.8765,3.0377,11.4017,3.0377,11.1088,3.33059L8.5,5.93936L8.5,0.75Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
