

.OptimizationDetail {
  :global {
    // Badge 组件
    .mc-badge .mc-badge-count {
      color: var(--mc-color-text);
      background-color: var(--mc-control-item-bg-hover);
      .mc-scroll-number-only-unit {
        color: var(--mc-color-text);
      }
    }
  }
  .functionsMenuTitle {
    font-weight: var(--mc-font-weight-strong);
  }
  .functionsMenuSubtitle {
    font-size: var(--mc-font-size-sm);
    color: var(--mc-color-text-secondary);
  }
  .functionsMenuExtra {
    font-size: var(--mc-font-size-sm);
    color: var(--mc-color-text-secondary);
    padding-left: var(--mc-padding-lg);
  }
  .functionsMenuContent {
    width: calc(100vw - 296px - calc(var(--mc-margin-lg) * 3));
  }
  .functionsMenuDivider {
    margin: 0;
  }
  .editorHeader {
    background-color: var(--mc-gray-1);
    padding: var(--mc-padding-sm) var(--mc-padding);
    border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
    border-radius: var(--mc-border-radius-lg) var(--mc-border-radius-lg) 0px 0px;
    .editorHeaderTitle {
      font-size: var(--mc-font-size);
      font-weight: var(--mc-font-weight-strong);
    }
    .editorCopy {
      color: var(--mc-color-text-secondary);
      cursor: pointer;
    }
  }
  .editorHeaderSubtitle {
    color: var(--mc-color-text-secondary);
  }
  .functionsMenuItem {
    color: var(--mc-color-text-secondary);
    height: var(--mc-font-height);
    line-height: var(--mc-font-height);
    margin: var(--mc-margin-xxs) var(--mc-margin) var(--mc-margin-xxs) 0;
  }
  .diffEditorHeader {
    padding: var(--mc-padding-sm);
    color: var(--mc-color-text-secondary);
    border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
    .diffEditorHeaderTitle {
      padding: calc(var(--mc-padding-xxs) / 2) var(--mc-padding-xxs);
      border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
      border-radius: var(--mc-border-radius-lg);
      font-size: var(--mc-font-size-sm);
      cursor: pointer;
    }
    .diffEditorHeaderButton {
      color: var(--mc-color-success);
      cursor: pointer;
    }
  }
}

.diffModal {
  .diffModalHeader {
    color: var(--mc-color-text-secondary);
    margin-bottom: var(--mc-margin-sm);
  }

  .diffModalContent {
    overflow: hidden;
    :global {
      .mc-checkbox + span {
        width: 95%;
        overflow: hidden;
      }
    }
  }
}

.editorHeaderCheckboxDesc {
  font-size: var(--mc-font-size-sm);
  color: var(--mc-color-text-secondary);
  .editorHeaderCheckboxDescItem {
    white-space: nowrap;
  }
  .editorHeaderCheckboxDescPath {
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.functionsMenuContent .diffEditorHeaderTitle {
  padding: var(--mc-padding-sm);
  gap: var(--mc-margin-xxs);
  .editorHeaderCheckboxTitle {
    font-weight: var(--mc-font-weight-strong);
  }
  .editorHeaderCheckboxDescPath {
    width: 250px;
  }
}
