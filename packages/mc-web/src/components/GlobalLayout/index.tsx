import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, Divider, Dropdown, DropdownProps, Flex, MenuProps, Result, Space, theme, Tooltip } from 'antd';
import {
  AndroidOutlined,
  AppleOutlined,
  HarmonyOSOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useAppData, useNavigate, useSearchParams } from 'ice';
import Cookie from 'js-cookie';

import { BasicLayout, BasicLayoutProps, ErrorBoundary, useLayoutContext } from '@ali/mc-uikit';
import useSendPV from '@/hooks/useSendPV';
import LeftSiderPanel from './LeftSiderPanel';
import { IterationsOutlined, PlusOutlined, QuestionOutlined, TriangleDownOutlined } from '@ali/mc-icons';
import UserInfoModal from './UserInfoModal';
import dayjs from 'dayjs';
import SurveyModal from '../SurveyModal';
import { CreateIterationDrawer } from '../CreateIteration';
import CreateModuleDrawer from '@/components/CreateModule';
// import Panel from '@/components/Assistant';

const createBtnItems: MenuProps['items'] = [
  {
    label: '创建迭代',
    key: 'iteration',
    icon: <IterationsOutlined />,
  },
  {
    type: 'divider',
  },
  {
    key: 'module_ANDROID',
    label: '创建 Android 模块',
    icon: <AndroidOutlined />,
  },
  {
    key: 'module_IOS',
    label: '创建 iOS 模块',
    icon: <AppleOutlined />,
  },
  {
    key: 'module_HARMONY',
    label: '创建 Harmony 模块',
    icon: <HarmonyOSOutlined />,
  },
];

function ErrorFallback() {
  const navigate = useNavigate();
  return (
    <Result
      status="500"
      title="发生了无法处理的渲染错误"
      subTitle="抱歉，页面发生了无法处理的渲染错误，请尝试刷新重试，如仍然无法恢复请联系管理员"
      extra={
        <Button
          type="primary"
          onClick={() => {
            navigate('/');
          }}
        >
          返回首页
        </Button>
      }
    />
  );
}


interface GlobalLayoutProps extends BasicLayoutProps {
  children: React.ReactNode;
}
export default function GlobalLayout(props: GlobalLayoutProps) {
  const { children, ...rest } = props;
  const [searchParams, setSearchParams] = useSearchParams();
  const [showUserModal, setShowUserModal] = useState<boolean>(searchParams.get('showUserInfo') === 'true');
  useSendPV();
  const { user } = useAppData() || {};
  const { token } = theme.useToken();

  // const [showPanel, setShowPanel] = useState<boolean>(false);
  // const triggerPanel = (status?: boolean) => {
  //   const _status = status === undefined ? !showPanel : status;
  //   setShowPanel(_status);
  // };
  const [openCreateIterationDrawer, setOpenCreateIterationDrawer] = useState<boolean>(false);
  const createModuleDrawerRef = useRef<{ openDrawer: (type: string) => void } | null>(null);

  const userMenuItems: MenuProps['items'] = useMemo(() => ([
    {
      key: 'userInfo',
      label: '用户信息',
    },
    ...(user?.isAdmin ? [{
      key: 'management',
      label: '管理后台',
    }] : []),
    {
      key: 'experiment',
      label: '切回旧版',
    },
  ]), [user]);

  const onMenuClick = useCallback(
    ({ key }: { key: string }) => {
      switch (key) {
        case 'experiment':
          Cookie.set('_mc_ng', 'false', { secure: true });
          window.location.reload();
          break;
        case 'userInfo':
          setShowUserModal(true);
          break;
        case 'management':
          window.open(`${window.location.origin}/#/manger/acl`, '_self');
          break;
      }
    }, []);

  const closeUserInfoModal = () => {
    setShowUserModal(false);
  };

  const createBtnMenuProps: DropdownProps['menu'] = {
    items: createBtnItems,
    onClick: (info) => {
      const { key } = info || {};
      if (key === 'iteration') {
        setOpenCreateIterationDrawer(true);
      } else if (key.startsWith('module_')) {
        const [, type] = key.split('_');
        createModuleDrawerRef.current?.openDrawer(type);
      }
    },
  };

  const { leftCollapsed, toggleLeftSiderPanel } = useLayoutContext();

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <BasicLayout
        headerLogoAddonBefore={
          <Button icon={leftCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />} onClick={toggleLeftSiderPanel} />
        }
        user={user}
        leftSiderPanel={<LeftSiderPanel />}
        userMenu={{
          items: userMenuItems,
          onClick: onMenuClick,
        }}
        // showGlobalSearch
        // floatButton={{
        //   showCompactMode: true,
        //   showDarkMode: true,
        //   shape: 'square',
        //   items: [
        //     {
        //       icon: <CustomerServiceOutlined />,
        //       onClick: () => {
        //         triggerPanel(true);
        //       },
        //     },
        //   ],
        // }}
        headerAddon={
          <>
            <Flex
              style={{ cursor: 'pointer' }}
              onClick={() => {
                window.location.href = '/#/doc?id=fothr9ne1pkgqn0x';
              }}
            >
              <img height="32px" src="https://img.alicdn.com/imgextra/i3/O1CN01pM2oqW1PbTEOg426u_!!6000000001859-2-tps-210-64.png" />
            </Flex>
          </>
        }
        headerUserAddonBefore={
          <Flex align="center" gap="small">
            <Divider type="vertical" />
            <Dropdown
              menu={createBtnMenuProps}
              overlayStyle={{
                width: 200,
              }}
              trigger={['click']}
            >
              <Button>
                <Space>
                  <PlusOutlined />
                  <TriangleDownOutlined />
                </Space>
              </Button>
            </Dropdown>
            <Tooltip title="使用文档">
              <QuestionOutlined
                size="middle"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  window.location.href = '/#/doc?id=ekecuxd55e7ubme0';
                }}
              />
            </Tooltip>
          </Flex>
        }
        headerUserAddonAfter={<SurveyModal />}
        showGlobalSearch
        // layoutAddonAfter={
        //   <ErrorBoundary>
        //     <Panel
        //       pathname={location.pathname}
        //       open={showPanel}
        //       onClose={() => triggerPanel(false)}
        //       onCapture={(capturing) => {
        //         triggerPanel(!capturing);
        //       }}
        //     />
        //   </ErrorBoundary>
        // }
        {...rest}
        footer={
          <Flex align="center" justify="center" style={{ color: token.colorTextSecondary }}>
            © {dayjs().year()} MTL Cloud - 淘天集团终端平台
          </Flex>
        }
      >
        {children}
      </BasicLayout>
      <UserInfoModal
        open={showUserModal}
        simpleMode={searchParams.get('showUserInfo') === 'true'}
        onClose={closeUserInfoModal}
        onCancel={closeUserInfoModal}
        onOk={() => {
          setSearchParams((prev) => {
            prev.delete('showUserInfo');
            return {
              ...prev,
            };
          });
          closeUserInfoModal();
        }}
      />
      <CreateIterationDrawer
        open={openCreateIterationDrawer}
        onClose={() => setOpenCreateIterationDrawer(false)}
        scope="GlobalHeader"
      />
      <CreateModuleDrawer ref={createModuleDrawerRef} />
    </ErrorBoundary>
  );
}
