import React from 'react';
import { Checkbox, Form, FormProps, Input } from 'antd';
import ReleaseStatusSelect from '@/components/Release/ReleaseStatusSelect';
import ReleasePublishTypeSelect from '@/components/Release/ReleasePublishTypeSelect';
import { UserSelect } from '@ali/mc-uikit';
import AlterSheetStatusSelect from '@/components/AlterSheet/AlterSheetStatusSelect';

export type ApplicationIterationSearchFormProps = {
  scope: string;
} & FormProps;
export default function ApplicationIterationSearchForm(props: ApplicationIterationSearchFormProps) {
  const { scope, ...formProps } = props;

  return (
    <Form layout="inline" {...formProps}>
      <Form.Item name="keyWord">
        <Input style={{ minWidth: 250 }} placeholder="关键词搜索名称或者版本号" allowClear />
      </Form.Item>
      {scope === 'ITERATE_PUBLISH' && (
        <>
        <Form.Item name="statusList">
          <ReleaseStatusSelect />
        </Form.Item>
        <Form.Item name="publishType">
          <ReleasePublishTypeSelect />
        </Form.Item>
        </>
      )}
      { scope === 'BIZ_DYNAMIC_PUBLISH' && (
        <Form.Item name="status">
          <AlterSheetStatusSelect placeholder="发布状态" />
        </Form.Item>
      )}
      { scope === 'APP_SELF_PUBLISH' && (
        <Form.Item name="status">
          <ReleaseStatusSelect />
        </Form.Item>
      )}
      {scope !== 'ITERATE_PUBLISH' && (
        <Form.Item name="creator">
          <UserSelect />
        </Form.Item>
      )}
      <Form.Item name="showMine" valuePropName="checked">
        <Checkbox>仅看我的</Checkbox>
      </Form.Item>
    </Form>
  );
}
