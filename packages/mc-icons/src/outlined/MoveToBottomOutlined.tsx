import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function MoveToBottomOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-move-to-bottom-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04608">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04608)">
          <path
            d="M7.469672384185791,10.7803C7.762562384185791,11.0732,8.23744238418579,11.0732,8.53033238418579,10.7803L12.280302384185791,7.03033C12.573202384185791,6.73744,12.573202384185791,6.26256,12.280302384185791,5.96967C11.98740238418579,5.67678,11.512602384185792,5.67678,11.21970238418579,5.96967L8.750002384185791,8.43934L8.750002384185791,1.75C8.750002384185791,1.33579,8.414212384185792,1,8.000002384185791,1C7.585792384185791,1,7.250002384185791,1.33579,7.250002384185791,1.75L7.250002384185791,8.43934L4.780332384185791,5.96967C4.487440384185791,5.67678,4.012560384185791,5.67678,3.719670384185791,5.96967C3.426779984185791,6.26256,3.426779984185791,6.73744,3.719670384185791,7.03033L7.469672384185791,10.7803Z"
            fill="currentColor"
          />
          <path
            d="M3.75,13.000100135803223C3.33579,13.000100135803223,3,13.335800135803222,3,13.750100135803223C3,14.164300135803222,3.33579,14.500100135803223,3.75,14.500100135803223L12.25,14.500100135803223C12.6642,14.500100135803223,13,14.164300135803222,13,13.750100135803223C13,13.335800135803222,12.6642,13.000100135803223,12.25,13.000100135803223L3.75,13.000100135803223Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
